
@extends('customer.layouts.app')

@section('title', 'طلباتي')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">طلباتي</h1>
        <div>
            <a href="{{ url('/') }}" class="btn btn-primary">
                <i class="bi bi-plus-circle me-1"></i> طلب جديد
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form action="{{ url('/customer/orders') }}" method="GET" class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label">بحث</label>
                    <input type="text" class="form-control" id="search" name="search" placeholder="رقم الطلب" value="{{ request('search') }}">
                </div>
                <div class="col-md-4">
                    <label for="status" class="form-label">الحالة</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">جميع الحالات</option>
                        <option value="pending">قيد الانتظار</option>
                        <option value="processing">قيد المعالجة</option>
                        <option value="shipped">قيد الشحن</option>
                        <option value="delivered">تم التوصيل</option>
                        <option value="cancelled">ملغي</option>
                        <option value="refunded">مسترجع</option>
                    </select>
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="bi bi-search me-1"></i> بحث
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Orders Table -->
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>رقم الطلب</th>
                            <th>التاريخ</th>
                            <th>المنتجات</th>
                            <th>المبلغ</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <a href="{{ url('/customer/orders/1254') }}" class="text-primary">#1254</a>
                            </td>
                            <td>2023/06/15</td>
                            <td>2 منتجات</td>
                            <td>1,250 ريال</td>
                            <td><span class="badge bg-warning">قيد المعالجة</span></td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ url('/customer/orders/1254') }}" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#trackOrderModal" data-id="1254">
                                        <i class="bi bi-geo-alt"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#cancelOrderModal" data-id="1254">
                                        <i class="bi bi-x-circle"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <a href="{{ url('/customer/orders/1253') }}" class="text-primary">#1253</a>
                            </td>
                            <td>2023/06/10</td>
                            <td>1 منتج</td>
                            <td>850 ريال</td>
                            <td><span class="badge bg-success">تم التوصيل</span></td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ url('/customer/orders/1253') }}" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#reorderModal" data-id="1253">
                                        <i class="bi bi-arrow-repeat"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#reviewModal" data-id="1253">
                                        <i class="bi bi-star"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <a href="{{ url('/customer/orders/1252') }}" class="text-primary">#1252</a>
                            </td>
                            <td>2023/06/05</td>
                            <td>3 منتجات</td>
                            <td>2,100 ريال</td>
                            <td><span class="badge bg-info">قيد الشحن</span></td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ url('/customer/orders/1252') }}" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#trackOrderModal" data-id="1252">
                                        <i class="bi bi-geo-alt"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#cancelOrderModal" data-id="1252">
                                        <i class="bi bi-x-circle"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <a href="{{ url('/customer/orders/1251') }}" class="text-primary">#1251</a>
                            </td>
                            <td>2023/05/28</td>
                            <td>1 منتج</td>
                            <td>750 ريال</td>
                            <td><span class="badge bg-danger">ملغي</span></td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ url('/customer/orders/1251') }}" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#reorderModal" data-id="1251">
                                        <i class="bi bi-arrow-repeat"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <a href="{{ url('/customer/orders/1250') }}" class="text-primary">#1250</a>
                            </td>
                            <td>2023/05/20</td>
                            <td>4 منتجات</td>
                            <td>3,500 ريال</td>
                            <td><span class="badge bg-success">تم التوصيل</span></td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ url('/customer/orders/1250') }}" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#reorderModal" data-id="1250">
                                        <i class="bi bi-arrow-repeat"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#reviewModal" data-id="1250">
                                        <i class="bi bi-star"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                    <li class="page-item disabled">
                        <a class="page-link" href="#" tabindex="-1" aria-disabled="true">السابق</a>
                    </li>
                    <li class="page-item active"><a class="page-link" href="#">1</a></li>
                    <li class="page-item"><a class="page-link" href="#">2</a></li>
                    <li class="page-item"><a class="page-link" href="#">3</a></li>
                    <li class="page-item">
                        <a class="page-link" href="#">التالي</a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>
</div>

<!-- Track Order Modal -->
<div class="modal fade" id="trackOrderModal" tabindex="-1" aria-labelledby="trackOrderModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="trackOrderModalLabel">تتبع الطلب #<span id="trackOrderId">1254</span></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="order-tracking">
                    <div class="tracking-step">
                        <div class="tracking-point bg-success"></div>
                        <div class="tracking-content">
                            <div class="tracking-date">2023/06/15 10:30</div>
                            <div class="tracking-title">تم استلام الطلب</div>
                            <div class="tracking-text">تم استلام الطلب بنجاح وجاري معالجته</div>
                        </div>
                    </div>
                    <div class="tracking-step active">
                        <div class="tracking-point bg-warning"></div>
                        <div class="tracking-content">
                            <div class="tracking-date">2023/06/15 14:45</div>
                            <div class="tracking-title">قيد المعالجة</div>
                            <div class="tracking-text">جاري تجهيز الطلب للشحن</div>
                        </div>
                    </div>
                    <div class="tracking-step">
                        <div class="tracking-point bg-secondary"></div>
                        <div class="tracking-content">
                            <div class="tracking-date">--:--</div>
                            <div class="tracking-title">قيد الشحن</div>
                            <div class="tracking-text">تم شحن الطلب وجاري توصيله</div>
                        </div>
                    </div>
                    <div class="tracking-step">
                        <div class="tracking-point bg-secondary"></div>
                        <div class="tracking-content">
                            <div class="tracking-date">--:--</div>
                            <div class="tracking-title">تم التوصيل</div>
                            <div class="tracking-text">تم توصيل الطلب بنجاح</div>
                        </div>
                    </div>
                </div>

                <hr>

                <div class="mb-3">
                    <h6 class="text-muted small mb-1">رقم التتبع</h6>
                    <div class="input-group">
                        <input type="text" class="form-control" value="TRK123456789" readonly>
                        <button class="btn btn-outline-primary" type="button" id="copyTrackingNumber">
                            <i class="bi bi-clipboard"></i>
                        </button>
                    </div>
                </div>

                <div>
                    <h6 class="text-muted small mb-1">شركة الشحن</h6>
                    <div>شركة أرامكس للشحن</div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <a href="{{ url('/customer/orders/1254') }}" class="btn btn-primary">تفاصيل الطلب</a>
            </div>
        </div>
    </div>
</div>

<!-- Cancel Order Modal -->
<div class="modal fade" id="cancelOrderModal" tabindex="-1" aria-labelledby="cancelOrderModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="cancelOrderModalLabel">إلغاء الطلب #<span id="cancelOrderId">1254</span></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من أنك تريد إلغاء هذا الطلب؟ لا يمكن التراجع عن هذا الإجراء.</p>

                <form id="cancelOrderForm">
                    @csrf
                    <input type="hidden" name="order_id" id="cancelOrderInputId" value="1254">

                    <div class="mb-3">
                        <label for="cancelReason" class="form-label">سبب الإلغاء</label>
                        <select class="form-select" id="cancelReason" name="reason" required>
                            <option value="">اختر سبب الإلغاء</option>
                            <option value="changed_mind">غيرت رأيي</option>
                            <option value="found_elsewhere">وجدت المنتج في مكان آخر</option>
                            <option value="delivery_time">وقت التسليم طويل جداً</option>
                            <option value="mistake">طلب بالخطأ</option>
                            <option value="other">سبب آخر</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="cancelNote" class="form-label">ملاحظات إضافية (اختياري)</label>
                        <textarea class="form-control" id="cancelNote" name="note" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirmCancelOrder">تأكيد الإلغاء</button>
            </div>
        </div>
    </div>
</div>

<!-- Reorder Modal -->
<div class="modal fade" id="reorderModal" tabindex="-1" aria-labelledby="reorderModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="reorderModalLabel">إعادة الطلب #<span id="reorderId">1253</span></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هل تريد إضافة جميع منتجات هذا الطلب إلى سلة التسوق الخاصة بك؟</p>

                <div class="list-group">
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">منتج مميز 2</h6>
                            <small class="text-muted">الكمية: 1</small>
                        </div>
                        <div>750 ريال</div>
                    </div>
                </div>

                <hr>

                <div class="d-flex justify-content-between">
                    <span>المجموع:</span>
                    <span class="fw-bold">750 ريال</span>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <a href="{{ url('/cart') }}" class="btn btn-primary">إضافة إلى السلة</a>
            </div>
        </div>
    </div>
</div>

<!-- Review Modal -->
<div class="modal fade" id="reviewModal" tabindex="-1" aria-labelledby="reviewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="reviewModalLabel">تقييم الطلب #<span id="reviewOrderId">1253</span></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>يرجى تقييم المنتجات التي طلبتها</p>

                <form id="reviewForm">
                    @csrf
                    <input type="hidden" name="order_id" id="reviewOrderInputId" value="1253">

                    <div class="mb-4">
                        <h6>منتج مميز 2</h6>
                        <div class="mb-2">
                            <div class="rating" data-product="1">
                                <i class="bi bi-star rating-star" data-rating="1"></i>
                                <i class="bi bi-star rating-star" data-rating="2"></i>
                                <i class="bi bi-star rating-star" data-rating="3"></i>
                                <i class="bi bi-star rating-star" data-rating="4"></i>
                                <i class="bi bi-star rating-star" data-rating="5"></i>
                            </div>
                            <input type="hidden" name="rating[1]" id="rating-1" value="0">
                        </div>
                        <textarea class="form-control" name="comment[1]" rows="2" placeholder="اكتب تعليقك..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="submitReview">إرسال التقييم</button>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Track Order Modal
        document.getElementById('trackOrderModal').addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const orderId = button.getAttribute('data-id');
            document.getElementById('trackOrderId').textContent = orderId;
        });

        // Cancel Order Modal
        document.getElementById('cancelOrderModal').addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const orderId = button.getAttribute('data-id');
            document.getElementById('cancelOrderId').textContent = orderId;
            document.getElementById('cancelOrderInputId').value = orderId;
        });

        // Reorder Modal
        document.getElementById('reorderModal').addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const orderId = button.getAttribute('data-id');
            document.getElementById('reorderId').textContent = orderId;
        });

        // Review Modal
        document.getElementById('reviewModal').addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const orderId = button.getAttribute('data-id');
            document.getElementById('reviewOrderId').textContent = orderId;
            document.getElementById('reviewOrderInputId').value = orderId;
        });

        // Copy Tracking Number
        document.getElementById('copyTrackingNumber').addEventListener('click', function() {
            const trackingNumber = document.querySelector('#trackOrderModal input[type="text"]').value;
            navigator.clipboard.writeText(trackingNumber);

            const originalInnerHTML = this.innerHTML;
            this.innerHTML = '<i class="bi bi-check"></i>';

            setTimeout(() => {
                this.innerHTML = originalInnerHTML;
            }, 2000);
        });

        // Rating Stars
        const ratingStars = document.querySelectorAll('.rating-star');
        ratingStars.forEach(star => {
            star.addEventListener('click', function() {
                const rating = this.getAttribute('data-rating');
                const product = this.closest('.rating').getAttribute('data-product');
                document.getElementById(`rating-${product}`).value = rating;

                // Update UI
                const stars = this.closest('.rating').querySelectorAll('.rating-star');
                stars.forEach((s, index) => {
                    if (index < rating) {
                        s.classList.remove('bi-star');
                        s.classList.add('bi-star-fill');
                    } else {
                        s.classList.remove('bi-star-fill');
                        s.classList.add('bi-star');
                    }
                });
            });
        });
    });
</script>
