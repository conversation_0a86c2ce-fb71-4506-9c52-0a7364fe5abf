
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ config('app.name', 'Ex Store') }} - @yield('title', 'لوحة تحكم المدير')</title>

    <!-- Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap">

    <!-- Styles -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.0/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.9.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="{{ asset('css/admin.css') }}">

    <style>
        :root {
            --primary-color: #000;
            --secondary-color: #fff;
            --accent-color: #e0e0e0;
            --sidebar-width: 250px;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background-color: var(--accent-color);
            color: var(--primary-color);
        }

        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            height: 100vh;
            width: var(--sidebar-width);
            background-color: var(--primary-color);
            color: var(--secondary-color);
            padding-top: 60px;
            z-index: 1000;
            transition: all 0.3s;
        }

        .sidebar.collapsed {
            width: 80px;
        }

        .sidebar .nav-link {
            color: var(--secondary-color);
            padding: 12px 20px;
            display: flex;
            align-items: center;
            transition: all 0.3s;
        }

        .sidebar .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--secondary-color);
        }

        .sidebar .nav-link.active {
            background-color: rgba(255, 255, 255, 0.2);
            border-right: 3px solid var(--secondary-color);
        }

        .sidebar .nav-link i {
            font-size: 1.2rem;
            margin-left: 10px;
            width: 20px;
            text-align: center;
        }

        .sidebar .nav-link span {
            transition: opacity 0.3s;
        }

        .sidebar.collapsed .nav-link span {
            opacity: 0;
            visibility: hidden;
        }

        .main-content {
            margin-right: var(--sidebar-width);
            padding: 20px;
            transition: all 0.3s;
            min-height: 100vh;
        }

        .main-content.expanded {
            margin-right: 80px;
        }

        .top-navbar {
            position: fixed;
            top: 0;
            right: var(--sidebar-width);
            left: 0;
            height: 60px;
            background-color: var(--secondary-color);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            padding: 0 20px;
            z-index: 999;
            transition: all 0.3s;
        }

        .top-navbar.expanded {
            right: 80px;
        }

        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }

        .card-header {
            background-color: var(--primary-color);
            color: var(--secondary-color);
            border-radius: 10px 10px 0 0 !important;
            padding: 15px 20px;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: var(--secondary-color);
        }

        .btn-primary:hover {
            background-color: #333;
            border-color: #333;
            color: var(--secondary-color);
        }

        .table {
            background-color: var(--secondary-color);
        }

        .table thead th {
            background-color: var(--primary-color);
            color: var(--secondary-color);
            border: none;
        }

        .badge {
            font-size: 0.85em;
        }

        .stats-card {
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            background-color: var(--secondary-color);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s;
        }

        .stats-card:hover {
            transform: translateY(-5px);
        }

        .stats-card .icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .stats-card.primary {
            border-right: 4px solid var(--primary-color);
        }

        .stats-card.success {
            border-right: 4px solid #28a745;
        }

        .stats-card.warning {
            border-right: 4px solid #ffc107;
        }

        .stats-card.danger {
            border-right: 4px solid #dc3545;
        }

        .toggle-sidebar {
            background: none;
            border: none;
            color: var(--primary-color);
            font-size: 1.5rem;
            cursor: pointer;
        }

        .user-dropdown {
            margin-right: auto;
        }

        .notification-icon {
            position: relative;
            margin-left: 15px;
            color: var(--primary-color);
            font-size: 1.2rem;
        }

        .notification-badge {
            position: absolute;
            top: -8px;
            left: -8px;
            background-color: #dc3545;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.7rem;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-sticky">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link {{ request()->is('admin') ? 'active' : '' }}" href="{{ url('/admin') }}">
                        <i class="bi bi-speedometer2"></i>
                        <span>الرئيسية</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ request()->is('admin/orders*') ? 'active' : '' }}" href="{{ url('/admin/orders') }}">
                        <i class="bi bi-cart-check"></i>
                        <span>الطلبات</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ request()->is('admin/products*') ? 'active' : '' }}" href="{{ url('/admin/products') }}">
                        <i class="bi bi-box"></i>
                        <span>المنتجات</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ request()->is('admin/categories*') ? 'active' : '' }}" href="{{ url('/admin/categories') }}">
                        <i class="bi bi-tags"></i>
                        <span>التصنيفات</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ request()->is('admin/customers*') ? 'active' : '' }}" href="{{ url('/admin/customers') }}">
                        <i class="bi bi-people"></i>
                        <span>العملاء</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ request()->is('admin/reports*') ? 'active' : '' }}" href="{{ url('/admin/reports') }}">
                        <i class="bi bi-graph-up"></i>
                        <span>التقارير</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ request()->is('admin/settings*') ? 'active' : '' }}" href="{{ url('/admin/settings') }}">
                        <i class="bi bi-gear"></i>
                        <span>الإعدادات</span>
                    </a>
                </li>
            </ul>

            <hr class="text-white">

            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link" href="{{ url('/') }}" target="_blank">
                        <i class="bi bi-shop"></i>
                        <span>عرض المتجر</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{{ route('logout') }}" onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                        <i class="bi bi-box-arrow-left"></i>
                        <span>تسجيل الخروج</span>
                        <form id="logout-form" action="{{ route('logout') }}" method="POST" style="display: none;">
                            @csrf
                        </form>
                    </a>
                </li>
            </ul>
        </div>
    </div>

    <!-- Top Navbar -->
    <nav class="top-navbar" id="top-navbar">
        <button class="toggle-sidebar" id="toggle-sidebar">
            <i class="bi bi-list"></i>
        </button>

        <div class="notification-icon">
            <i class="bi bi-bell"></i>
            <span class="notification-badge">3</span>
        </div>

        <div class="user-dropdown">
            <div class="dropdown">
                <button class="btn btn-light dropdown-toggle d-flex align-items-center" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="bi bi-person-circle me-2"></i>
                    {{ Auth::user()->name }}
                </button>
                <ul class="dropdown-menu dropdown-menu-start" aria-labelledby="userDropdown">
                    <li><a class="dropdown-item" href="{{ url('/admin/profile') }}"><i class="bi bi-person me-2"></i> الملف الشخصي</a></li>
                    <li><a class="dropdown-item" href="{{ url('/admin/settings') }}"><i class="bi bi-gear me-2"></i> الإعدادات</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <a class="dropdown-item" href="{{ route('logout') }}" onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                            <i class="bi bi-box-arrow-left me-2"></i> تسجيل الخروج
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content" id="main-content">
        @yield('content')
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ asset('js/admin.js') }}"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('main-content');
            const topNavbar = document.getElementById('top-navbar');
            const toggleSidebar = document.getElementById('toggle-sidebar');

            toggleSidebar.addEventListener('click', function() {
                sidebar.classList.toggle('collapsed');
                mainContent.classList.toggle('expanded');
                topNavbar.classList.toggle('expanded');
            });
        });
    </script>

    @yield('scripts')
</body>
</html>
