
@extends('admin.layouts.app')

@section('title', 'المنتجات')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">المنتجات</h1>
        <a href="{{ url('/admin/products/create') }}" class="btn btn-primary">
            <i class="bi bi-plus-circle me-1"></i> إضافة منتج جديد
        </a>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form action="{{ url('/admin/products') }}" method="GET" class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label">بحث</label>
                    <input type="text" class="form-control" id="search" name="search" placeholder="اسم المنتج أو الكود" value="{{ request('search') }}">
                </div>
                <div class="col-md-3">
                    <label for="category" class="form-label">التصنيف</label>
                    <select class="form-select" id="category" name="category">
                        <option value="">جميع التصنيفات</option>
                        <option value="1">إلكترونيات</option>
                        <option value="2">أزياء</option>
                        <option value="3">منزل</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="status" class="form-label">الحالة</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">جميع الحالات</option>
                        <option value="active">نشط</option>
                        <option value="inactive">غير نشط</option>
                        <option value="out_of_stock">نفد من المخزون</option>
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="bi bi-search me-1"></i> بحث
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Products Table -->
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>
                                <input type="checkbox" class="form-check-input" id="selectAll">
                            </th>
                            <th>المنتج</th>
                            <th>السعر</th>
                            <th>المخزون</th>
                            <th>الحالة</th>
                            <th>تاريخ الإضافة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <input type="checkbox" class="form-check-input product-checkbox" value="1">
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <img src="{{ asset('images/products/product1.jpg') }}" class="rounded me-2" width="50" height="50" alt="منتج">
                                    <div>
                                        <h6 class="mb-0">منتج مميز 1</h6>
                                        <div class="text-muted small">كود: PRD001</div>
                                    </div>
                                </div>
                            </td>
                            <td>500 ريال</td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <span class="me-2">45</span>
                                    <div class="progress" style="height: 5px; width: 50px;">
                                        <div class="progress-bar bg-success" role="progressbar" style="width: 75%" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                </div>
                            </td>
                            <td><span class="badge bg-success">نشط</span></td>
                            <td>2023/06/01</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ url('/admin/products/1') }}" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <a href="{{ url('/admin/products/1/edit') }}" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-outline-danger delete-product" data-id="1">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <input type="checkbox" class="form-check-input product-checkbox" value="2">
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <img src="{{ asset('images/products/product2.jpg') }}" class="rounded me-2" width="50" height="50" alt="منتج">
                                    <div>
                                        <h6 class="mb-0">منتج مميز 2</h6>
                                        <div class="text-muted small">كود: PRD002</div>
                                    </div>
                                </div>
                            </td>
                            <td>750 ريال</td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <span class="me-2">12</span>
                                    <div class="progress" style="height: 5px; width: 50px;">
                                        <div class="progress-bar bg-warning" role="progressbar" style="width: 30%" aria-valuenow="30" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                </div>
                            </td>
                            <td><span class="badge bg-success">نشط</span></td>
                            <td>2023/06/02</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ url('/admin/products/2') }}" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <a href="{{ url('/admin/products/2/edit') }}" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-outline-danger delete-product" data-id="2">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <input type="checkbox" class="form-check-input product-checkbox" value="3">
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <img src="{{ asset('images/products/product3.jpg') }}" class="rounded me-2" width="50" height="50" alt="منتج">
                                    <div>
                                        <h6 class="mb-0">منتج مميز 3</h6>
                                        <div class="text-muted small">كود: PRD003</div>
                                    </div>
                                </div>
                            </td>
                            <td>350 ريال</td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <span class="me-2">0</span>
                                    <div class="progress" style="height: 5px; width: 50px;">
                                        <div class="progress-bar bg-danger" role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                </div>
                            </td>
                            <td><span class="badge bg-danger">نفد من المخزون</span></td>
                            <td>2023/06/03</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ url('/admin/products/3') }}" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <a href="{{ url('/admin/products/3/edit') }}" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-outline-danger delete-product" data-id="3">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <input type="checkbox" class="form-check-input product-checkbox" value="4">
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <img src="{{ asset('images/products/product4.jpg') }}" class="rounded me-2" width="50" height="50" alt="منتج">
                                    <div>
                                        <h6 class="mb-0">منتج مميز 4</h6>
                                        <div class="text-muted small">كود: PRD004</div>
                                    </div>
                                </div>
                            </td>
                            <td>900 ريال</td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <span class="me-2">67</span>
                                    <div class="progress" style="height: 5px; width: 50px;">
                                        <div class="progress-bar bg-success" role="progressbar" style="width: 90%" aria-valuenow="90" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                </div>
                            </td>
                            <td><span class="badge bg-success">نشط</span></td>
                            <td>2023/06/04</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ url('/admin/products/4') }}" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <a href="{{ url('/admin/products/4/edit') }}" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-outline-danger delete-product" data-id="4">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Bulk Actions -->
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div>
                    <select class="form-select form-select-sm" id="bulkActions" style="width: auto; display: inline-block;">
                        <option value="">الإجراءات المجمعه</option>
                        <option value="active">تفعيل</option>
                        <option value="inactive">تعطيل</option>
                        <option value="delete">حذف</option>
                    </select>
                    <button type="button" class="btn btn-sm btn-outline-primary ms-2" id="applyBulkActions">تطبيق</button>
                </div>

                <!-- Pagination -->
                <nav aria-label="Page navigation">
                    <ul class="pagination pagination-sm">
                        <li class="page-item disabled">
                            <a class="page-link" href="#" tabindex="-1" aria-disabled="true">السابق</a>
                        </li>
                        <li class="page-item active"><a class="page-link" href="#">1</a></li>
                        <li class="page-item"><a class="page-link" href="#">2</a></li>
                        <li class="page-item"><a class="page-link" href="#">3</a></li>
                        <li class="page-item">
                            <a class="page-link" href="#">التالي</a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- Delete Product Modal -->
<div class="modal fade" id="deleteProductModal" tabindex="-1" aria-labelledby="deleteProductModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteProductModalLabel">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                هل أنت متأكد من رغبتك في حذف هذا المنتج؟ لا يمكن التراجع عن هذا الإجراء.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteProductForm" action="" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>

@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Select all checkboxes
        const selectAllCheckbox = document.getElementById('selectAll');
        const productCheckboxes = document.querySelectorAll('.product-checkbox');

        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function() {
                productCheckboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
            });
        }

        // Delete product
        const deleteButtons = document.querySelectorAll('.delete-product');
        const deleteProductModal = new bootstrap.Modal(document.getElementById('deleteProductModal'));
        const deleteProductForm = document.getElementById('deleteProductForm');

        deleteButtons.forEach(button => {
            button.addEventListener('click', function() {
                const productId = this.getAttribute('data-id');
                deleteProductForm.setAttribute('action', `/admin/products/${productId}`);
                deleteProductModal.show();
            });
        });

        // Bulk actions
        const applyBulkActionsBtn = document.getElementById('applyBulkActions');
        const bulkActionsSelect = document.getElementById('bulkActions');

        applyBulkActionsBtn.addEventListener('click', function() {
            const selectedAction = bulkActionsSelect.value;
            if (!selectedAction) {
                alert('الرجاء اختيار إجراء');
                return;
            }

            const selectedProducts = [];
            productCheckboxes.forEach(checkbox => {
                if (checkbox.checked) {
                    selectedProducts.push(checkbox.value);
                }
            });

            if (selectedProducts.length === 0) {
                alert('الرجاء اختيار منتج واحد على الأقل');
                return;
            }

            if (confirm(`هل أنت متأكد من تطبيق الإجراء "${selectedAction}" على المنتجات المختارة؟`)) {
                // Here you would normally submit a form with the selected products and action
                alert(`تم تطبيق الإجراء "${selectedAction}" على ${selectedProducts.length} منتجات`);
            }
        });
    });
</script>
@endsection
