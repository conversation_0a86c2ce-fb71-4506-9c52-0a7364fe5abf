
@extends('admin.layouts.app')

@section('title', 'الطلبات')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">الطلبات</h1>
        <div>
            <button class="btn btn-outline-primary me-2" id="exportOrders">
                <i class="bi bi-download me-1"></i> تصدير
            </button>
            <button class="btn btn-outline-primary" id="printOrders">
                <i class="bi bi-printer me-1"></i> طباعة
            </button>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form action="{{ url('/admin/orders') }}" method="GET" class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label">بحث</label>
                    <input type="text" class="form-control" id="search" name="search" placeholder="رقم الطلب أو اسم العميل" value="{{ request('search') }}">
                </div>
                <div class="col-md-3">
                    <label for="status" class="form-label">الحالة</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">جميع الحالات</option>
                        <option value="pending">قيد الانتظار</option>
                        <option value="processing">قيد المعالجة</option>
                        <option value="shipped">قيد الشحن</option>
                        <option value="delivered">تم التوصيل</option>
                        <option value="cancelled">ملغي</option>
                        <option value="refunded">مسترجع</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="date_from" class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" value="{{ request('date_from') }}">
                </div>
                <div class="col-md-3">
                    <label for="date_to" class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" value="{{ request('date_to') }}">
                </div>
                <div class="col-12">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-search me-1"></i> بحث
                    </button>
                    <button type="button" class="btn btn-outline-secondary ms-2" id="resetFilters">
                        <i class="bi bi-arrow-clockwise me-1"></i> إعادة تعيين
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Orders Table -->
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>
                                <input type="checkbox" class="form-check-input" id="selectAll">
                            </th>
                            <th>رقم الطلب</th>
                            <th>العميل</th>
                            <th>التاريخ</th>
                            <th>المبلغ</th>
                            <th>طريقة الدفع</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <input type="checkbox" class="form-check-input order-checkbox" value="1254">
                            </td>
                            <td>
                                <a href="{{ url('/admin/orders/1254') }}" class="text-primary">#1254</a>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 32px; height: 32px;">
                                        <span class="text-white fw-bold">أ</span>
                                    </div>
                                    <div>
                                        <div>أحمد محمد</div>
                                        <div class="text-muted small"><EMAIL></div>
                                    </div>
                                </div>
                            </td>
                            <td>2023/06/15</td>
                            <td>1,250 ريال</td>
                            <td>
                                <span class="badge bg-info">الدفع عند الاستلام</span>
                            </td>
                            <td>
                                <select class="form-select form-select-sm order-status" data-id="1254">
                                    <option value="pending">قيد الانتظار</option>
                                    <option value="processing" selected>قيد المعالجة</option>
                                    <option value="shipped">قيد الشحن</option>
                                    <option value="delivered">تم التوصيل</option>
                                    <option value="cancelled">ملغي</option>
                                    <option value="refunded">مسترجع</option>
                                </select>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ url('/admin/orders/1254') }}" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <a href="{{ url('/admin/orders/1254/edit') }}" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-outline-danger delete-order" data-id="1254">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <input type="checkbox" class="form-check-input order-checkbox" value="1253">
                            </td>
                            <td>
                                <a href="{{ url('/admin/orders/1253') }}" class="text-primary">#1253</a>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="bg-success rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 32px; height: 32px;">
                                        <span class="text-white fw-bold">م</span>
                                    </div>
                                    <div>
                                        <div>محمد علي</div>
                                        <div class="text-muted small"><EMAIL></div>
                                    </div>
                                </div>
                            </td>
                            <td>2023/06/14</td>
                            <td>850 ريال</td>
                            <td>
                                <span class="badge bg-warning">بطاقة ائتمان</span>
                            </td>
                            <td>
                                <select class="form-select form-select-sm order-status" data-id="1253">
                                    <option value="pending">قيد الانتظار</option>
                                    <option value="processing">قيد المعالجة</option>
                                    <option value="shipped">قيد الشحن</option>
                                    <option value="delivered" selected>تم التوصيل</option>
                                    <option value="cancelled">ملغي</option>
                                    <option value="refunded">مسترجع</option>
                                </select>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ url('/admin/orders/1253') }}" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <a href="{{ url('/admin/orders/1253/edit') }}" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-outline-danger delete-order" data-id="1253">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <input type="checkbox" class="form-check-input order-checkbox" value="1252">
                            </td>
                            <td>
                                <a href="{{ url('/admin/orders/1252') }}" class="text-primary">#1252</a>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="bg-danger rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 32px; height: 32px;">
                                        <span class="text-white fw-bold">ف</span>
                                    </div>
                                    <div>
                                        <div>فهد سعود</div>
                                        <div class="text-muted small"><EMAIL></div>
                                    </div>
                                </div>
                            </td>
                            <td>2023/06/14</td>
                            <td>2,100 ريال</td>
                            <td>
                                <span class="badge bg-info">الدفع عند الاستلام</span>
                            </td>
                            <td>
                                <select class="form-select form-select-sm order-status" data-id="1252">
                                    <option value="pending">قيد الانتظار</option>
                                    <option value="processing">قيد المعالجة</option>
                                    <option value="shipped" selected>قيد الشحن</option>
                                    <option value="delivered">تم التوصيل</option>
                                    <option value="cancelled">ملغي</option>
                                    <option value="refunded">مسترجع</option>
                                </select>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ url('/admin/orders/1252') }}" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <a href="{{ url('/admin/orders/1252/edit') }}" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-outline-danger delete-order" data-id="1252">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <input type="checkbox" class="form-check-input order-checkbox" value="1251">
                            </td>
                            <td>
                                <a href="{{ url('/admin/orders/1251') }}" class="text-primary">#1251</a>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="bg-warning rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 32px; height: 32px;">
                                        <span class="text-white fw-bold">خ</span>
                                    </div>
                                    <div>
                                        <div>خالد عبدالله</div>
                                        <div class="text-muted small"><EMAIL></div>
                                    </div>
                                </div>
                            </td>
                            <td>2023/06/13</td>
                            <td>750 ريال</td>
                            <td>
                                <span class="badge bg-warning">بطاقة ائتمان</span>
                            </td>
                            <td>
                                <select class="form-select form-select-sm order-status" data-id="1251">
                                    <option value="pending">قيد الانتظار</option>
                                    <option value="processing">قيد المعالجة</option>
                                    <option value="shipped">قيد الشحن</option>
                                    <option value="delivered">تم التوصيل</option>
                                    <option value="cancelled" selected>ملغي</option>
                                    <option value="refunded">مسترجع</option>
                                </select>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ url('/admin/orders/1251') }}" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <a href="{{ url('/admin/orders/1251/edit') }}" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-outline-danger delete-order" data-id="1251">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <input type="checkbox" class="form-check-input order-checkbox" value="1250">
                            </td>
                            <td>
                                <a href="{{ url('/admin/orders/1250') }}" class="text-primary">#1250</a>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="bg-info rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 32px; height: 32px;">
                                        <span class="text-white fw-bold">ع</span>
                                    </div>
                                    <div>
                                        <div>عبدالرحمن أحمد</div>
                                        <div class="text-muted small"><EMAIL></div>
                                    </div>
                                </div>
                            </td>
                            <td>2023/06/13</td>
                            <td>3,500 ريال</td>
                            <td>
                                <span class="badge bg-info">الدفع عند الاستلام</span>
                            </td>
                            <td>
                                <select class="form-select form-select-sm order-status" data-id="1250">
                                    <option value="pending">قيد الانتظار</option>
                                    <option value="processing">قيد المعالجة</option>
                                    <option value="shipped">قيد الشحن</option>
                                    <option value="delivered" selected>تم التوصيل</option>
                                    <option value="cancelled">ملغي</option>
                                    <option value="refunded">مسترجع</option>
                                </select>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ url('/admin/orders/1250') }}" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <a href="{{ url('/admin/orders/1250/edit') }}" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-outline-danger delete-order" data-id="1250">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                    <li class="page-item disabled">
                        <a class="page-link" href="#" tabindex="-1" aria-disabled="true">السابق</a>
                    </li>
                    <li class="page-item active"><a class="page-link" href="#">1</a></li>
                    <li class="page-item"><a class="page-link" href="#">2</a></li>
                    <li class="page-item"><a class="page-link" href="#">3</a></li>
                    <li class="page-item">
                        <a class="page-link" href="#">التالي</a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>
</div>

<!-- Delete Order Modal -->
<div class="modal fade" id="deleteOrderModal" tabindex="-1" aria-labelledby="deleteOrderModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteOrderModalLabel">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                هل أنت متأكد من حذف هذا الطلب؟ لا يمكن التراجع عن هذا الإجراء.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteOrderForm" action="" method="POST">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>

@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Select all checkboxes
        const selectAllCheckbox = document.getElementById('selectAll');
        const orderCheckboxes = document.querySelectorAll('.order-checkbox');

        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function() {
                orderCheckboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
            });
        }

        // Delete order
        const deleteOrderModal = new bootstrap.Modal(document.getElementById('deleteOrderModal'));
        const deleteOrderButtons = document.querySelectorAll('.delete-order');
        const deleteOrderForm = document.getElementById('deleteOrderForm');

        deleteOrderButtons.forEach(button => {
            button.addEventListener('click', function() {
                const orderId = this.getAttribute('data-id');
                deleteOrderForm.setAttribute('action', `/admin/orders/${orderId}`);
                deleteOrderModal.show();
            });
        });

        // Update order status
        const orderStatusSelects = document.querySelectorAll('.order-status');

        orderStatusSelects.forEach(select => {
            select.addEventListener('change', function() {
                const orderId = this.getAttribute('data-id');
                const status = this.value;

                fetch(`/admin/orders/${orderId}/status`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({ status: status })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Show success message
                        const toast = new bootstrap.Toast(document.getElementById('successToast'));
                        document.querySelector('#successToast .toast-body').textContent = 'تم تحديث حالة الطلب بنجاح';
                        toast.show();
                    } else {
                        // Show error message
                        const toast = new bootstrap.Toast(document.getElementById('errorToast'));
                        document.querySelector('#errorToast .toast-body').textContent = 'حدث خطأ أثناء تحديث حالة الطلب';
                        toast.show();
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    // Show error message
                    const toast = new bootstrap.Toast(document.getElementById('errorToast'));
                    document.querySelector('#errorToast .toast-body').textContent = 'حدث خطأ أثناء تحديث حالة الطلب';
                    toast.show();
                });
            });
        });

        // Reset filters
        const resetFiltersButton = document.getElementById('resetFilters');

        if (resetFiltersButton) {
            resetFiltersButton.addEventListener('click', function() {
                window.location.href = '/admin/orders';
            });
        }

        // Export orders
        const exportOrdersButton = document.getElementById('exportOrders');

        if (exportOrdersButton) {
            exportOrdersButton.addEventListener('click', function() {
                window.location.href = '/admin/orders/export?' + new URLSearchParams(new FormData(document.querySelector('form')));
            });
        }

        // Print orders
        const printOrdersButton = document.getElementById('printOrders');

        if (printOrdersButton) {
            printOrdersButton.addEventListener('click', function() {
                window.print();
            });
        }
    });
</script>
@endsection
