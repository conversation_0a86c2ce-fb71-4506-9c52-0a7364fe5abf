
/* General Styles */
:root {
    --primary-color: #000;
    --secondary-color: #fff;
    --accent-color: #e0e0e0;
}

body {
    font-family: 'Cairo', sans-serif;
    background-color: var(--secondary-color);
    color: var(--primary-color);
}

/* Navbar */
.navbar {
    background-color: var(--primary-color);
    color: var(--secondary-color);
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.navbar-light .navbar-nav .nav-link {
    color: var(--secondary-color);
}

.navbar-light .navbar-nav .nav-link:hover {
    color: var(--accent-color);
}

/* Footer */
.footer {
    background-color: var(--primary-color);
    color: var(--secondary-color);
    padding: 2rem 0;
    margin-top: 3rem;
}

/* Buttons */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--secondary-color);
}

.btn-primary:hover {
    background-color: #333;
    border-color: #333;
    color: var(--secondary-color);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--secondary-color);
}

/* Cards */
.card {
    border: 1px solid var(--accent-color);
    margin-bottom: 1.5rem;
}

/* Products */
.product-card {
    transition: transform 0.3s ease;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.product-image {
    height: 200px;
    object-fit: cover;
}

/* Badges */
.badge {
    background-color: var(--primary-color);
}

/* Cart */
.cart-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: #ff0000;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

/* Forms */
.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(0, 0, 0, 0.25);
}

.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(0, 0, 0, 0.25);
}

/* Tables */
.table {
    background-color: var(--secondary-color);
}

.table thead th {
    background-color: var(--primary-color);
    color: var(--secondary-color);
    border: none;
}

/* Alerts */
.alert {
    border: none;
    border-radius: 0.5rem;
}

/* Modals */
.modal-content {
    border: none;
    border-radius: 0.5rem;
}

.modal-header {
    background-color: var(--primary-color);
    color: var(--secondary-color);
    border-radius: 0.5rem 0.5rem 0 0;
}

/* Pagination */
.page-link {
    color: var(--primary-color);
}

.page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Breadcrumb */
.breadcrumb-item + .breadcrumb-item::before {
    content: ">";
    color: var(--primary-color);
}

/* Responsive */
@media (max-width: 768px) {
    .product-image {
        height: 150px;
    }
}
