
// Admin Dashboard Functions
document.addEventListener('DOMContentLoaded', function() {
    // Initialize sidebar toggle
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('main-content');
    const topNavbar = document.getElementById('top-navbar');
    const toggleSidebar = document.getElementById('toggle-sidebar');

    if (toggleSidebar) {
        toggleSidebar.addEventListener('click', function() {
            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('expanded');
            topNavbar.classList.toggle('expanded');

            // Save state to localStorage
            localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
        });
    }

    // Load sidebar state from localStorage
    if (localStorage.getItem('sidebarCollapsed') === 'true') {
        sidebar.classList.add('collapsed');
        mainContent.classList.add('expanded');
        topNavbar.classList.add('expanded');
    }

    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize popovers
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Initialize select all checkbox
    const selectAllCheckbox = document.getElementById('selectAll');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.product-checkbox, .order-checkbox, .customer-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });
    }

    // Initialize order status change
    const orderStatusSelects = document.querySelectorAll('.order-status');
    orderStatusSelects.forEach(select => {
        select.addEventListener('change', function() {
            const orderId = this.getAttribute('data-id');
            const status = this.value;

            // Update order status via AJAX
            fetch(`/admin/orders/${orderId}/status`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify({
                    status: status
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('تم تحديث حالة الطلب بنجاح', 'success');
                } else {
                    showToast('حدث خطأ أثناء تحديث حالة الطلب', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('حدث خطأ أثناء تحديث حالة الطلب', 'error');
            });
        });
    });

    // Initialize delete product buttons
    const deleteProductButtons = document.querySelectorAll('.delete-product');
    deleteProductButtons.forEach(button => {
        button.addEventListener('click', function() {
            const productId = this.getAttribute('data-id');

            if (confirm('هل أنت متأكد من أنك تريد حذف هذا المنتج؟')) {
                // Delete product via AJAX
                fetch(`/admin/products/${productId}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Remove the row from the table
                        const row = document.getElementById(`product-row-${productId}`);
                        if (row) {
                            row.remove();
                        }

                        // Show success message
                        showToast('تم حذف المنتج بنجاح', 'success');
                    } else {
                        showToast('حدث خطأ أثناء حذف المنتج', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showToast('حدث خطأ أثناء حذف المنتج', 'error');
                });
            }
        });
    });

    // Initialize delete order buttons
    const deleteOrderButtons = document.querySelectorAll('.delete-order');
    deleteOrderButtons.forEach(button => {
        button.addEventListener('click', function() {
            const orderId = this.getAttribute('data-id');

            if (confirm('هل أنت متأكد من أنك تريد حذف هذا الطلب؟')) {
                // Delete order via AJAX
                fetch(`/admin/orders/${orderId}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Remove the row from the table
                        const row = document.getElementById(`order-row-${orderId}`);
                        if (row) {
                            row.remove();
                        }

                        // Show success message
                        showToast('تم حذف الطلب بنجاح', 'success');
                    } else {
                        showToast('حدث خطأ أثناء حذف الطلب', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showToast('حدث خطأ أثناء حذف الطلب', 'error');
                });
            }
        });
    });

    // Initialize delete customer buttons
    const deleteCustomerButtons = document.querySelectorAll('.delete-customer');
    deleteCustomerButtons.forEach(button => {
        button.addEventListener('click', function() {
            const customerId = this.getAttribute('data-id');

            if (confirm('هل أنت متأكد من أنك تريد حذف هذا العميل؟')) {
                // Delete customer via AJAX
                fetch(`/admin/customers/${customerId}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Remove the row from the table
                        const row = document.getElementById(`customer-row-${customerId}`);
                        if (row) {
                            row.remove();
                        }

                        // Show success message
                        showToast('تم حذف العميل بنجاح', 'success');
                    } else {
                        showToast('حدث خطأ أثناء حذف العميل', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showToast('حدث خطأ أثناء حذف العميل', 'error');
                });
            }
        });
    });

    // Initialize bulk actions
    const bulkActionButtons = document.querySelectorAll('.bulk-action');
    bulkActionButtons.forEach(button => {
        button.addEventListener('click', function() {
            const action = this.getAttribute('data-action');
            const type = this.getAttribute('data-type');
            const selectedItems = Array.from(document.querySelectorAll(`.${type}-checkbox:checked`)).map(checkbox => checkbox.value);

            if (selectedItems.length === 0) {
                showToast('الرجاء تحديد عنصر واحد على الأقل', 'warning');
                return;
            }

            if (confirm(`هل أنت متأكد من أنك تريد ${action === 'delete' ? 'حذف' : action === 'activate' ? 'تفعيل' : 'تعطيل'} العناصر المحددة؟`)) {
                // Perform bulk action via AJAX
                fetch(`/admin/${type}/bulk`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    },
                    body: JSON.stringify({
                        action: action,
                        items: selectedItems
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Remove the rows from the table or update status
                        selectedItems.forEach(itemId => {
                            const row = document.getElementById(`${type}-row-${itemId}`);
                            if (row) {
                                if (action === 'delete') {
                                    row.remove();
                                } else {
                                    const statusBadge = row.querySelector('.status-badge');
                                    if (statusBadge) {
                                        statusBadge.className = `badge ${action === 'activate' ? 'bg-success' : 'bg-secondary'}`;
                                        statusBadge.textContent = action === 'activate' ? 'نشط' : 'غير نشط';
                                    }
                                }
                            }
                        });

                        // Show success message
                        showToast(`تم ${action === 'delete' ? 'حذف' : action === 'activate' ? 'تفعيل' : 'تعطيل'} العناصر بنجاح`, 'success');

                        // Uncheck select all checkbox
                        const selectAllCheckbox = document.getElementById('selectAll');
                        if (selectAllCheckbox) {
                            selectAllCheckbox.checked = false;
                        }
                    } else {
                        showToast(`حدث خطأ أثناء ${action === 'delete' ? 'حذف' : action === 'activate' ? 'تفعيل' : 'تعطيل'} العناصر`, 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showToast(`حدث خطأ أثناء ${action === 'delete' ? 'حذف' : action === 'activate' ? 'تفعيل' : 'تعطيل'} العناصر`, 'error');
                });
            }
        });
    });

    // Initialize export buttons
    const exportButtons = document.querySelectorAll('[id$="Export"]');
    exportButtons.forEach(button => {
        button.addEventListener('click', function() {
            const type = this.getAttribute('data-type');
            window.location.href = `/admin/${type}/export`;
        });
    });

    // Initialize print buttons
    const printButtons = document.querySelectorAll('[id$="Print"]');
    printButtons.forEach(button => {
        button.addEventListener('click', function() {
            const type = this.getAttribute('data-type');
            window.open(`/admin/${type}/print`, '_blank');
        });
    });

    // Initialize reset filters button
    const resetFiltersButton = document.getElementById('resetFilters');
    if (resetFiltersButton) {
        resetFiltersButton.addEventListener('click', function() {
            window.location.href = window.location.pathname;
        });
    }
});

// Utility Functions
function showToast(message, type = 'info') {
    // Create toast element
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type === 'success' ? 'success' : type === 'error' ? 'danger' : type === 'warning' ? 'warning' : 'primary'} border-0`;
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');

    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    `;

    // Get or create toast container
    let toastContainer = document.getElementById('toastContainer');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toastContainer';
        toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        document.body.appendChild(toastContainer);
    }

    // Add toast to container
    toastContainer.appendChild(toast);

    // Initialize and show toast
    const bsToast = new bootstrap.Toast(toast, {
        autohide: true,
        delay: 3000
    });
    bsToast.show();

    // Remove toast element after it's hidden
    toast.addEventListener('hidden.bs.toast', () => {
        toast.remove();
    });
}
