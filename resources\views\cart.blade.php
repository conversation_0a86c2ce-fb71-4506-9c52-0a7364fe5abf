
@extends('layouts.app')

@section('title', 'سلة التسوق')

@section('content')
<div class="container py-5">
    <h1 class="mb-4">سلة التسوق</h1>

    @if (Cart::count() == 0)
        <div class="text-center py-5">
            <i class="bi bi-cart-x fs-1 text-muted"></i>
            <h3 class="mt-3">سلة التسوق فارغة</h3>
            <p class="text-muted">يبدو أنك لم تقم بإضافة أي منتجات إلى سلة التسوق بعد.</p>
            <a href="{{ url('/products') }}" class="btn btn-primary mt-3">
                <i class="bi bi-arrow-left me-1"></i> العودة للتسوق
            </a>
        </div>
    @else
        <div class="row">
            <div class="col-lg-8">
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>المنتج</th>
                                        <th>السعر</th>
                                        <th>الكمية</th>
                                        <th>الإجمالي</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <img src="{{ asset('images/products/product1.jpg') }}" class="rounded me-3" width="80" height="80" alt="منتج">
                                                <div>
                                                    <h6 class="mb-0">منتج مميز 1</h6>
                                                    <div class="text-muted small">كود: PRD001</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>500 ريال</td>
                                        <td>
                                            <div class="input-group" style="width: 120px;">
                                                <button class="btn btn-outline-secondary btn-sm" type="button" onclick="updateQuantity(1, 'decrease')">
                                                    <i class="bi bi-dash"></i>
                                                </button>
                                                <input type="text" class="form-control text-center" id="quantity-1" value="1" readonly>
                                                <button class="btn btn-outline-secondary btn-sm" type="button" onclick="updateQuantity(1, 'increase')">
                                                    <i class="bi bi-plus"></i>
                                                </button>
                                            </div>
                                        </td>
                                        <td>500 ريال</td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-danger" onclick="removeFromCart(1)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <img src="{{ asset('images/products/product2.jpg') }}" class="rounded me-3" width="80" height="80" alt="منتج">
                                                <div>
                                                    <h6 class="mb-0">منتج مميز 2</h6>
                                                    <div class="text-muted small">كود: PRD002</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>750 ريال</td>
                                        <td>
                                            <div class="input-group" style="width: 120px;">
                                                <button class="btn btn-outline-secondary btn-sm" type="button" onclick="updateQuantity(2, 'decrease')">
                                                    <i class="bi bi-dash"></i>
                                                </button>
                                                <input type="text" class="form-control text-center" id="quantity-2" value="1" readonly>
                                                <button class="btn btn-outline-secondary btn-sm" type="button" onclick="updateQuantity(2, 'increase')">
                                                    <i class="bi bi-plus"></i>
                                                </button>
                                            </div>
                                        </td>
                                        <td>750 ريال</td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-danger" onclick="removeFromCart(2)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="d-flex justify-content-between mt-4">
                            <a href="{{ url('/products') }}" class="btn btn-outline-primary">
                                <i class="bi bi-arrow-left me-1"></i> العودة للتسوق
                            </a>
                            <button class="btn btn-outline-danger" onclick="clearCart()">
                                <i class="bi bi-trash me-1"></i> إفراغ السلة
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Recommended Products -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">قد يعجبك أيضاً</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 col-sm-6 mb-4">
                                <div class="card product-card h-100">
                                    <img src="{{ asset('images/products/product5.jpg') }}" class="card-img-top product-image" alt="منتج">
                                    <div class="card-body">
                                        <h5 class="card-title">منتج مقترح 1</h5>
                                        <p class="card-text">وصف قصير للمنتج المقترح الأول</p>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span class="price fw-bold">450 ريال</span>
                                            <button class="btn btn-sm btn-primary" onclick="addToCart(5)">
                                                <i class="bi bi-cart-plus"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-6 mb-4">
                                <div class="card product-card h-100">
                                    <img src="{{ asset('images/products/product6.jpg') }}" class="card-img-top product-image" alt="منتج">
                                    <div class="card-body">
                                        <h5 class="card-title">منتج مقترح 2</h5>
                                        <p class="card-text">وصف قصير للمنتج المقترح الثاني</p>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span class="price fw-bold">650 ريال</span>
                                            <button class="btn btn-sm btn-primary" onclick="addToCart(6)">
                                                <i class="bi bi-cart-plus"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-6 mb-4">
                                <div class="card product-card h-100">
                                    <img src="{{ asset('images/products/product7.jpg') }}" class="card-img-top product-image" alt="منتج">
                                    <div class="card-body">
                                        <h5 class="card-title">منتج مقترح 3</h5>
                                        <p class="card-text">وصف قصير للمنتج المقترح الثالث</p>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span class="price fw-bold">550 ريال</span>
                                            <button class="btn btn-sm btn-primary" onclick="addToCart(7)">
                                                <i class="bi bi-cart-plus"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">ملخص الطلب</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between mb-2">
                            <span>المجموع الفرعي:</span>
                            <span>1,250 ريال</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>الشحن:</span>
                            <span>0 ريال</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>الضريبة:</span>
                            <span>0 ريال</span>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between fw-bold fs-5">
                            <span>الإجمالي:</span>
                            <span>1,250 ريال</span>
                        </div>

                        <form action="{{ url('/checkout') }}" method="POST" class="mt-4">
                            @csrf
                            <div class="mb-3">
                                <label for="couponCode" class="form-label">كود الخصم</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="couponCode" name="coupon_code" placeholder="أدخل كود الخصم">
                                    <button class="btn btn-outline-secondary" type="button">تطبيق</button>
                                </div>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-lock me-1"></i> إتمام الطلب
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Shipping Info -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">معلومات الشحن</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <h6 class="mb-0">عنوان الشحن</h6>
                                <a href="{{ url('/customer/addresses') }}" class="btn btn-sm btn-outline-primary">تغيير</a>
                            </div>
                            <p class="mb-0">أحمد محمد<br>شارع الملك فهد، حي النخيل<br>الرياض، المملكة العربية السعودية<br>الرمز البريدي: 12345</p>
                        </div>

                        <div>
                            <h6 class="mb-2">طريقة الشحن</h6>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="radio" name="shippingMethod" id="standardShipping" value="standard" checked>
                                <label class="form-check-label" for="standardShipping">
                                    شحن عادي (5-7 أيام عمل) - مجاني
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="shippingMethod" id="expressShipping" value="express">
                                <label class="form-check-label" for="expressShipping">
                                    شحن سريع (2-3 أيام عمل) - 25 ريال
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>

@section('scripts')
<script>
    function updateQuantity(productId, action) {
        const quantityInput = document.getElementById(`quantity-${productId}`);
        let quantity = parseInt(quantityInput.value);

        if (action === 'increase') {
            quantity++;
        } else if (action === 'decrease' && quantity > 1) {
            quantity--;
        }

        quantityInput.value = quantity;

        // Here you would normally send an AJAX request to update the cart
        // For demo purposes, we'll just update the UI
        updateCartTotals();
    }

    function removeFromCart(productId) {
        if (confirm('هل أنت متأكد من أنك تريد إزالة هذا المنتج من السلة؟')) {
            // Here you would normally send an AJAX request to remove the item from the cart
            // For demo purposes, we'll just reload the page
            location.reload();
        }
    }

    function clearCart() {
        if (confirm('هل أنت متأكد من أنك تريد إفراغ السلة؟')) {
            // Here you would normally send an AJAX request to clear the cart
            // For demo purposes, we'll just reload the page
            location.reload();
        }
    }

    function addToCart(productId) {
        // Here you would normally send an AJAX request to add the item to the cart
        // For demo purposes, we'll just show a success message
        alert('تمت إضافة المنتج إلى السلة بنجاح!');
    }

    function updateCartTotals() {
        // This function would normally recalculate the cart totals based on the quantities
        // For demo purposes, we'll just leave it empty
    }
</script>
@endsection
