
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ config('app.name', 'Ex Store') }} - @yield('title', 'حسابي')</title>

    <!-- Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap">

    <!-- Styles -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.0/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.9.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="{{ asset('css/customer.css') }}">

    <style>
        :root {
            --primary-color: #000;
            --secondary-color: #fff;
            --accent-color: #e0e0e0;
            --sidebar-width: 250px;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background-color: var(--accent-color);
            color: var(--primary-color);
        }

        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            height: 100vh;
            width: var(--sidebar-width);
            background-color: var(--primary-color);
            color: var(--secondary-color);
            padding-top: 60px;
            z-index: 1000;
            transition: all 0.3s;
        }

        .sidebar.collapsed {
            width: 80px;
        }

        .sidebar .nav-link {
            color: var(--secondary-color);
            padding: 12px 20px;
            display: flex;
            align-items: center;
            transition: all 0.3s;
        }

        .sidebar .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--secondary-color);
        }

        .sidebar .nav-link.active {
            background-color: rgba(255, 255, 255, 0.2);
            border-right: 3px solid var(--secondary-color);
        }

        .sidebar .nav-link i {
            font-size: 1.2rem;
            margin-left: 10px;
            width: 20px;
            text-align: center;
        }

        .sidebar .nav-link span {
            transition: opacity 0.3s;
        }

        .sidebar.collapsed .nav-link span {
            opacity: 0;
            visibility: hidden;
        }

        .main-content {
            margin-right: var(--sidebar-width);
            padding: 20px;
            transition: all 0.3s;
            min-height: 100vh;
        }

        .main-content.expanded {
            margin-right: 80px;
        }

        .top-navbar {
            position: fixed;
            top: 0;
            right: var(--sidebar-width);
            left: 0;
            height: 60px;
            background-color: var(--secondary-color);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            padding: 0 20px;
            z-index: 999;
            transition: all 0.3s;
        }

        .top-navbar.expanded {
            right: 80px;
        }

        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }

        .card-header {
            background-color: var(--primary-color);
            color: var(--secondary-color);
            border-radius: 10px 10px 0 0 !important;
            padding: 15px 20px;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: var(--secondary-color);
        }

        .btn-primary:hover {
            background-color: #333;
            border-color: #333;
            color: var(--secondary-color);
        }

        .table {
            background-color: var(--secondary-color);
        }

        .table thead th {
            background-color: var(--primary-color);
            color: var(--secondary-color);
            border: none;
        }

        .badge {
            font-size: 0.85em;
        }

        .toggle-sidebar {
            background: none;
            border: none;
            color: var(--primary-color);
            font-size: 1.5rem;
            cursor: pointer;
        }

        .user-dropdown {
            margin-right: auto;
        }

        .notification-icon {
            position: relative;
            margin-left: 15px;
            color: var(--primary-color);
            font-size: 1.2rem;
        }

        .notification-badge {
            position: absolute;
            top: -8px;
            left: -8px;
            background-color: #dc3545;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.7rem;
        }

        .order-tracking {
            position: relative;
            padding-right: 30px;
        }

        .order-tracking:before {
            content: '';
            position: absolute;
            top: 0;
            right: 15px;
            height: 100%;
            width: 2px;
            background-color: #e9ecef;
        }

        .tracking-step {
            position: relative;
            margin-bottom: 30px;
        }

        .tracking-point {
            position: absolute;
            right: 0;
            top: 0;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            z-index: 1;
        }

        .tracking-content {
            margin-right: 50px;
        }

        .tracking-date {
            font-size: 0.85rem;
            color: #6c757d;
            margin-bottom: 5px;
        }

        .tracking-title {
            font-weight: 600;
            margin-bottom: 5px;
        }

        .tracking-text {
            font-size: 0.9rem;
            color: #6c757d;
        }

        .tracking-step.active .tracking-point {
            box-shadow: 0 0 0 4px rgba(0, 0, 0, 0.2);
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-sticky">
            <div class="text-center p-3 border-bottom border-secondary">
                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center mx-auto mb-2" style="width: 60px; height: 60px;">
                    <span class="text-white fw-bold fs-4">{{ substr(Auth::user()->name, 0, 1) }}</span>
                </div>
                <h6>{{ Auth::user()->name }}</h6>
                <small class="text-white-50">{{ Auth::user()->email }}</small>
            </div>
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link {{ request()->is('customer') ? 'active' : '' }}" href="{{ url('/customer') }}">
                        <i class="bi bi-speedometer2"></i>
                        <span>الرئيسية</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ request()->is('customer/orders*') ? 'active' : '' }}" href="{{ url('/customer/orders') }}">
                        <i class="bi bi-cart-check"></i>
                        <span>طلباتي</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ request()->is('customer/wishlist*') ? 'active' : '' }}" href="{{ url('/customer/wishlist') }}">
                        <i class="bi bi-heart"></i>
                        <span>المفضلة</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ request()->is('customer/profile*') ? 'active' : '' }}" href="{{ url('/customer/profile') }}">
                        <i class="bi bi-person"></i>
                        <span>ملفي الشخصي</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ request()->is('customer/addresses*') ? 'active' : '' }}" href="{{ url('/customer/addresses') }}">
                        <i class="bi bi-geo-alt"></i>
                        <span>عناويني</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ request()->is('customer/settings*') ? 'active' : '' }}" href="{{ url('/customer/settings') }}">
                        <i class="bi bi-gear"></i>
                        <span>الإعدادات</span>
                    </a>
                </li>
            </ul>

            <hr class="text-white">

            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link" href="{{ url('/') }}" target="_blank">
                        <i class="bi bi-shop"></i>
                        <span>عرض المتجر</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{{ route('logout') }}" onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                        <i class="bi bi-box-arrow-left"></i>
                        <span>تسجيل الخروج</span>
                        <form id="logout-form" action="{{ route('logout') }}" method="POST" style="display: none;">
                            @csrf
                        </form>
                    </a>
                </li>
            </ul>
        </div>
    </div>

    <!-- Top Navbar -->
    <nav class="top-navbar" id="top-navbar">
        <button class="toggle-sidebar" id="toggle-sidebar">
            <i class="bi bi-list"></i>
        </button>

        <div class="notification-icon">
            <i class="bi bi-bell"></i>
            <span class="notification-badge">2</span>
        </div>

        <div class="user-dropdown">
            <div class="dropdown">
                <button class="btn btn-light dropdown-toggle d-flex align-items-center" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="bi bi-person-circle me-2"></i>
                    {{ Auth::user()->name }}
                </button>
                <ul class="dropdown-menu dropdown-menu-start" aria-labelledby="userDropdown">
                    <li><a class="dropdown-item" href="{{ url('/customer/profile') }}"><i class="bi bi-person me-2"></i> الملف الشخصي</a></li>
                    <li><a class="dropdown-item" href="{{ url('/customer/settings') }}"><i class="bi bi-gear me-2"></i> الإعدادات</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <a class="dropdown-item" href="{{ route('logout') }}" onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                            <i class="bi bi-box-arrow-left me-2"></i> تسجيل الخروج
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content" id="main-content">
        @yield('content')
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ asset('js/customer.js') }}"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('main-content');
            const topNavbar = document.getElementById('top-navbar');
            const toggleSidebar = document.getElementById('toggle-sidebar');

            toggleSidebar.addEventListener('click', function() {
                sidebar.classList.toggle('collapsed');
                mainContent.classList.toggle('expanded');
                topNavbar.classList.toggle('expanded');
            });
        });
    </script>

    @yield('scripts')
</body>
</html>
