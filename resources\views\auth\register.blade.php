
@extends('layouts.app')

@section('title', 'إنشاء حساب')

@section('content')
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card">
                <div class="card-body p-5">
                    <h3 class="text-center mb-4">إنشاء حساب جديد</h3>

                    <form method="POST" action="{{ route('register') }}">
                        @csrf

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">الاسم الكامل</label>
                                <input id="name" type="text" class="form-control @error('name') is-invalid @enderror" name="name" value="{{ old('name') }}" required autocomplete="name" autofocus>
                                @error('name')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">رقم الهاتف</label>
                                <input id="phone" type="tel" class="form-control @error('phone') is-invalid @enderror" name="phone" value="{{ old('phone') }}" required autocomplete="phone">
                                @error('phone')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <input id="email" type="email" class="form-control @error('email') is-invalid @enderror" name="email" value="{{ old('email') }}" required autocomplete="email">
                            @error('email')
                                <span class="invalid-feedback" role="alert">
                                    <strong>{{ $message }}</strong>
                                </span>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="password" class="form-label">كلمة المرور</label>
                            <input id="password" type="password" class="form-control @error('password') is-invalid @enderror" name="password" required autocomplete="new-password">
                            @error('password')
                                <span class="invalid-feedback" role="alert">
                                    <strong>{{ $message }}</strong>
                                </span>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="password-confirm" class="form-label">تأكيد كلمة المرور</label>
                            <input id="password-confirm" type="password" class="form-control" name="password_confirmation" required autocomplete="new-password">
                        </div>

                        <div class="mb-3 form-check">
                            <input class="form-check-input" type="checkbox" name="terms" id="terms" required>
                            <label class="form-check-label" for="terms">
                                أوافق على <a href="#" data-bs-toggle="modal" data-bs-target="#termsModal">الشروط والأحكام</a>
                            </label>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                إنشاء حساب
                            </button>
                        </div>
                    </form>

                    <hr class="my-4">

                    <div class="text-center">
                        <p>لديك حساب بالفعل؟ <a href="{{ route('login') }}">تسجيل الدخول</a></p>
                    </div>

                    <div class="text-center mt-4">
                        <p class="text-muted">أو سجل باستخدام</p>
                        <div class="d-flex justify-content-center">
                            <a href="#" class="btn btn-outline-primary me-2">
                                <i class="bi bi-facebook"></i>
                            </a>
                            <a href="#" class="btn btn-outline-primary me-2">
                                <i class="bi bi-google"></i>
                            </a>
                            <a href="#" class="btn btn-outline-primary">
                                <i class="bi bi-twitter"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Terms and Conditions Modal -->
<div class="modal fade" id="termsModal" tabindex="-1" aria-labelledby="termsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="termsModalLabel">الشروط والأحكام</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <h5>مقدمة</h5>
                <p>مرحباً بك في متجر Ex الإلكتروني. باستخدامك لموقعنا، فإنك توافق على الالتزام بالشروط والأحكام التالية.</p>

                <h5>المعلومات الشخصية</h5>
                <p>عند التسجيل في متجرنا، نطلب منك تقديم بعض المعلومات الشخصية مثل الاسم والبريد الإلكتروني ورقم الهاتف. نلتزم بحماية هذه المعلومات وعدم مشاركتها مع أطراف ثالثة دون موافقتك.</p>

                <h5>المنتجات والأسعار</h5>
                <p>نسعى جاهدين لضمان دقة معلومات المنتجات والأسعار المعروضة على موقعنا. ومع ذلك، نحتفظ بالحق في تعديل أسعار المنتجات في أي وقت دون إشعار مسبق.</p>

                <h5>الطلبات والدفع</h5>
                <p>عند إتمام عملية الشراء، فإنك تؤكد أنك تملك السلطة القانونية لاستخدام بطاقة الدفع أو أي وسيلة دفع أخرى تستخدمها. نستخدم وسائل دفع آمنة لحماية معلوماتك المالية.</p>

                <h5>الشحن والتوصيل</h5>
                <p>نسعى لتوصيل طلباتك في أسرع وقت ممكن. ومع ذلك، لا نتحمل مسؤولية أي تأخير في التوصيل يحدث بسبب أسباب خارجة عن إرادتنا.</p>

                <h5>الإرجاع والاستبدال</h5>
                <p>يمكنك إرجاع أو استبدال المنتجات التي قمت بشرائها خلال 14 يوماً من تاريخ الاستلام، شريطة أن تكون المنتجات في حالتها الأصلية ولم يتم استخدامها.</p>

                <h5>الملكية الفكرية</h5>
                <p>جميع المحتويات المعروضة على موقع متجر Ex، بما في ذلك النصوص والصور والشعارات، هي ملكية فكرية محمية لصالح متجر Ex ولا يجوز استخدامها دون إذن كتابي مسبق.</p>

                <h5>تعديل الشروط والأحكام</h5>
                <p>نحتفظ بالحق في تعديل هذه الشروط والأحكام في أي وقت. سيتم نشر أي تعديلات على هذه الصفحة، وتنطبق التعديلات فور نشرها.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
@endsection
