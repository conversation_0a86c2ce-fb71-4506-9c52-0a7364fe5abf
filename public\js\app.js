
// Cart Functions
function updateQuantity(productId, action) {
    const quantityInput = document.getElementById(`quantity-${productId}`);
    let quantity = parseInt(quantityInput.value);

    if (action === 'increase') {
        quantity++;
    } else if (action === 'decrease' && quantity > 1) {
        quantity--;
    }

    quantityInput.value = quantity;

    // Update cart via AJAX
    fetch(`/cart/update/${productId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        },
        body: JSON.stringify({
            quantity: quantity
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update cart total
            document.getElementById('cart-total').innerText = data.total + ' ريال';

            // Update cart count in navbar
            document.querySelector('.cart-count').innerText = data.count;

            // Update item total
            const itemTotal = document.getElementById(`item-total-${productId}`);
            if (itemTotal) {
                itemTotal.innerText = (data.price * quantity) + ' ريال';
            }

            // Show success message
            showToast('تم تحديث السلة بنجاح', 'success');
        } else {
            showToast('حدث خطأ أثناء تحديث السلة', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('حدث خطأ أثناء تحديث السلة', 'error');
    });
}

function removeFromCart(productId) {
    if (confirm('هل أنت متأكد من أنك تريد إزالة هذا المنتج من السلة؟')) {
        // Remove item from cart via AJAX
        fetch(`/cart/remove/${productId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Remove the row from the table
                const row = document.getElementById(`cart-row-${productId}`);
                if (row) {
                    row.remove();
                }

                // Update cart total
                document.getElementById('cart-total').innerText = data.total + ' ريال';

                // Update cart count in navbar
                document.querySelector('.cart-count').innerText = data.count;

                // Show success message
                showToast('تم إزالة المنتج من السلة بنجاح', 'success');

                // Check if cart is empty
                if (data.count === 0) {
                    location.reload();
                }
            } else {
                showToast('حدث خطأ أثناء إزالة المنتج من السلة', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('حدث خطأ أثناء إزالة المنتج من السلة', 'error');
        });
    }
}

function clearCart() {
    if (confirm('هل أنت متأكد من أنك تريد إفراغ السلة؟')) {
        // Clear cart via AJAX
        fetch('/cart/clear', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success message
                showToast('تم إفراغ السلة بنجاح', 'success');

                // Reload page after a short delay
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                showToast('حدث خطأ أثناء إفراغ السلة', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('حدث خطأ أثناء إفراغ السلة', 'error');
        });
    }
}

function addToCart(productId) {
    // Add item to cart via AJAX
    fetch(`/cart/add/${productId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update cart count in navbar
            document.querySelector('.cart-count').innerText = data.count;

            // Show success message
            showToast('تم إضافة المنتج إلى السلة بنجاح', 'success');
        } else {
            showToast('حدث خطأ أثناء إضافة المنتج إلى السلة', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('حدث خطأ أثناء إضافة المنتج إلى السلة', 'error');
    });
}

// Wishlist Functions
function addToWishlist(productId) {
    // Add item to wishlist via AJAX
    fetch(`/wishlist/add/${productId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update wishlist icon
            const wishlistIcon = document.getElementById(`wishlist-icon-${productId}`);
            if (wishlistIcon) {
                wishlistIcon.classList.remove('bi-heart');
                wishlistIcon.classList.add('bi-heart-fill');
                wishlistIcon.classList.add('text-danger');
            }

            // Show success message
            showToast('تم إضافة المنتج إلى المفضلة بنجاح', 'success');
        } else {
            showToast('حدث خطأ أثناء إضافة المنتج إلى المفضلة', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('حدث خطأ أثناء إضافة المنتج إلى المفضلة', 'error');
    });
}

function removeFromWishlist(productId) {
    if (confirm('هل أنت متأكد من أنك تريد إزالة هذا المنتج من المفضلة؟')) {
        // Remove item from wishlist via AJAX
        fetch(`/wishlist/remove/${productId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Remove the item from the DOM
                const item = document.getElementById(`wishlist-item-${productId}`);
                if (item) {
                    item.remove();
                }

                // Show success message
                showToast('تم إزالة المنتج من المفضلة بنجاح', 'success');

                // Check if wishlist is empty
                const wishlistItems = document.querySelectorAll('[id^="wishlist-item-"]');
                if (wishlistItems.length === 0) {
                    document.getElementById('wishlist-empty').style.display = 'block';
                    document.getElementById('wishlist-items').style.display = 'none';
                }
            } else {
                showToast('حدث خطأ أثناء إزالة المنتج من المفضلة', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('حدث خطأ أثناء إزالة المنتج من المفضلة', 'error');
        });
    }
}

// Utility Functions
function showToast(message, type = 'info') {
    // Create toast element
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'primary'} border-0`;
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');

    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    `;

    // Get or create toast container
    let toastContainer = document.getElementById('toastContainer');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toastContainer';
        toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        document.body.appendChild(toastContainer);
    }

    // Add toast to container
    toastContainer.appendChild(toast);

    // Initialize and show toast
    const bsToast = new bootstrap.Toast(toast, {
        autohide: true,
        delay: 3000
    });
    bsToast.show();

    // Remove toast element after it's hidden
    toast.addEventListener('hidden.bs.toast', () => {
        toast.remove();
    });
}

// Document Ready
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    });

    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'))
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl)
    });

    // Product image gallery
    const productImages = document.querySelectorAll('.product-thumbnail');
    if (productImages.length > 0) {
        productImages.forEach(thumbnail => {
            thumbnail.addEventListener('click', function() {
                const mainImage = document.getElementById('main-product-image');
                if (mainImage) {
                    mainImage.src = this.src;

                    // Update active state
                    productImages.forEach(img => img.classList.remove('active'));
                    this.classList.add('active');
                }
            });
        });
    }

    // Product quantity selector
    const quantityInputs = document.querySelectorAll('.quantity-input');
    quantityInputs.forEach(input => {
        const decreaseBtn = input.parentElement.querySelector('.quantity-decrease');
        const increaseBtn = input.parentElement.querySelector('.quantity-increase');

        if (decreaseBtn) {
            decreaseBtn.addEventListener('click', function() {
                let value = parseInt(input.value);
                if (value > 1) {
                    input.value = value - 1;
                }
            });
        }

        if (increaseBtn) {
            increaseBtn.addEventListener('click', function() {
                let value = parseInt(input.value);
                input.value = value + 1;
            });
        }
    });

    // Form validation
    const forms = document.querySelectorAll('.needs-validation');
    forms.forEach(form => {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });
});
