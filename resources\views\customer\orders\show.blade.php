
@extends('customer.layouts.app')

@section('title', 'تفاصيل الطلب #1254')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">تفاصيل الطلب #1254</h1>
        <div>
            <a href="{{ url('/customer/orders') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-right me-1"></i> العودة للطلبات
            </a>
            <button class="btn btn-primary ms-2" data-bs-toggle="modal" data-bs-target="#reorderModal">
                <i class="bi bi-arrow-repeat me-1"></i> إعادة الطلب
            </button>
        </div>
    </div>

    <div class="row">
        <!-- Order Details -->
        <div class="col-lg-8 mb-4">
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">تفاصيل الطلب</h5>
                    <span class="badge bg-warning">قيد المعالجة</span>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>المنتج</th>
                                    <th>السعر</th>
                                    <th>الكمية</th>
                                    <th>الإجمالي</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <img src="{{ asset('images/products/product1.jpg') }}" class="rounded me-2" width="50" height="50" alt="منتج">
                                            <div>
                                                <div>منتج مميز 1</div>
                                                <div class="text-muted small">كود: PRD001</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>500 ريال</td>
                                    <td>1</td>
                                    <td>500 ريال</td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <img src="{{ asset('images/products/product2.jpg') }}" class="rounded me-2" width="50" height="50" alt="منتج">
                                            <div>
                                                <div>منتج مميز 2</div>
                                                <div class="text-muted small">كود: PRD002</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>750 ريال</td>
                                    <td>1</td>
                                    <td>750 ريال</td>
                                </tr>
                            </tbody>
                            <tfoot>
                                <tr>
                                    <td colspan="3" class="text-end">المجموع الفرعي:</td>
                                    <td>1,250 ريال</td>
                                </tr>
                                <tr>
                                    <td colspan="3" class="text-end">الشحن:</td>
                                    <td>0 ريال</td>
                                </tr>
                                <tr>
                                    <td colspan="3" class="text-end">الضريبة:</td>
                                    <td>0 ريال</td>
                                </tr>
                                <tr>
                                    <td colspan="3" class="text-end fw-bold">الإجمالي:</td>
                                    <td class="fw-bold">1,250 ريال</td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Order Timeline -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">مسار الطلب</h5>
                </div>
                <div class="card-body">
                    <div class="order-tracking">
                        <div class="tracking-step">
                            <div class="tracking-point bg-success"></div>
                            <div class="tracking-content">
                                <div class="tracking-date">2023/06/15 10:30</div>
                                <div class="tracking-title">تم استلام الطلب</div>
                                <div class="tracking-text">تم استلام الطلب بنجاح وجاري معالجته</div>
                            </div>
                        </div>
                        <div class="tracking-step active">
                            <div class="tracking-point bg-warning"></div>
                            <div class="tracking-content">
                                <div class="tracking-date">2023/06/15 14:45</div>
                                <div class="tracking-title">قيد المعالجة</div>
                                <div class="tracking-text">جاري تجهيز الطلب للشحن</div>
                            </div>
                        </div>
                        <div class="tracking-step">
                            <div class="tracking-point bg-secondary"></div>
                            <div class="tracking-content">
                                <div class="tracking-date">--:--</div>
                                <div class="tracking-title">قيد الشحن</div>
                                <div class="tracking-text">تم شحن الطلب وجاري توصيله</div>
                            </div>
                        </div>
                        <div class="tracking-step">
                            <div class="tracking-point bg-secondary"></div>
                            <div class="tracking-content">
                                <div class="tracking-date">--:--</div>
                                <div class="tracking-title">تم التوصيل</div>
                                <div class="tracking-text">تم توصيل الطلب بنجاح</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Order Information -->
        <div class="col-lg-4 mb-4">
            <!-- Order Summary -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">معلومات الطلب</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6 class="text-muted small mb-1">رقم الطلب</h6>
                        <div>#1254</div>
                    </div>
                    <div class="mb-3">
                        <h6 class="text-muted small mb-1">تاريخ الطلب</h6>
                        <div>2023/06/15</div>
                    </div>
                    <div class="mb-3">
                        <h6 class="text-muted small mb-1">حالة الطلب</h6>
                        <div>
                            <span class="badge bg-warning">قيد المعالجة</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <h6 class="text-muted small mb-1">طريقة الدفع</h6>
                        <div>
                            <span class="badge bg-info">الدفع عند الاستلام</span>
                        </div>
                    </div>
                    <div>
                        <h6 class="text-muted small mb-1">ملاحظات</h6>
                        <div>يرجى التوصيل في المساء</div>
                    </div>
                </div>
            </div>

            <!-- Shipping Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">معلومات الشحن</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6 class="text-muted small mb-1">عنوان الشحن</h6>
                        <div>أحمد محمد</div>
                        <div>شارع الملك فهد، حي النخيل</div>
                        <div>الرياض، المملكة العربية السعودية</div>
                        <div>الرمز البريدي: 12345</div>
                    </div>
                    <div>
                        <h6 class="text-muted small mb-1">معلومات التتبع</h6>
                        <div class="input-group">
                            <input type="text" class="form-control" placeholder="رقم التتبع" value="TRK123456789" readonly>
                            <button class="btn btn-outline-primary" type="button" onclick="window.open('https://track.12345.com/TRK123456789', '_blank')">تتبع</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">الإجراءات</h5>
                </div>
                <div class="card-body">
                    <button class="btn btn-outline-primary w-100 mb-2" data-bs-toggle="modal" data-bs-target="#cancelOrderModal">
                        <i class="bi bi-x-circle me-1"></i> إلغاء الطلب
                    </button>
                    <button class="btn btn-outline-primary w-100 mb-2" data-bs-toggle="modal" data-bs-target="#contactSupportModal">
                        <i class="bi bi-chat-dots me-1"></i> تواصل مع الدعم
                    </button>
                    <button class="btn btn-outline-primary w-100" data-bs-toggle="modal" data-bs-target="#downloadInvoiceModal">
                        <i class="bi bi-download me-1"></i> تحميل الفاتورة
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Cancel Order Modal -->
<div class="modal fade" id="cancelOrderModal" tabindex="-1" aria-labelledby="cancelOrderModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="cancelOrderModalLabel">إلغاء الطلب #1254</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من أنك تريد إلغاء هذا الطلب؟ لا يمكن التراجع عن هذا الإجراء.</p>
                <form>
                    <div class="mb-3">
                        <label for="cancelReason" class="form-label">سبب الإلغاء</label>
                        <select class="form-select" id="cancelReason" required>
                            <option value="" selected disabled>اختر سبب الإلغاء</option>
                            <option value="found_elsewhere">وجدت المنتج في مكان آخر</option>
                            <option value="price_changed">تغير سعر المنتج</option>
                            <option value="delivery_time">وقت التوصيل طويل جداً</option>
                            <option value="mistake_order">طلب بالخطأ</option>
                            <option value="other">سبب آخر</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="cancelNote" class="form-label">ملاحظات إضافية (اختياري)</label>
                        <textarea class="form-control" id="cancelNote" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger">تأكيد الإلغاء</button>
            </div>
        </div>
    </div>
</div>

<!-- Contact Support Modal -->
<div class="modal fade" id="contactSupportModal" tabindex="-1" aria-labelledby="contactSupportModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="contactSupportModalLabel">تواصل مع الدعم الفني</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>يمكنك التواصل مع فريق الدعم الفني لدينا حول أي استفسار بخصوص طلبك.</p>
                <form>
                    <div class="mb-3">
                        <label for="supportSubject" class="form-label">الموضوع</label>
                        <input type="text" class="form-control" id="supportSubject" required>
                    </div>
                    <div class="mb-3">
                        <label for="supportMessage" class="form-label">الرسالة</label>
                        <textarea class="form-control" id="supportMessage" rows="4" required></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary">إرسال</button>
            </div>
        </div>
    </div>
</div>

<!-- Download Invoice Modal -->
<div class="modal fade" id="downloadInvoiceModal" tabindex="-1" aria-labelledby="downloadInvoiceModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="downloadInvoiceModalLabel">تحميل الفاتورة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>يمكنك تحميل فاتورة طلبك بصيغة PDF.</p>
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-outline-primary">
                        <i class="bi bi-file-earmark-pdf me-1"></i> تحميل الفاتورة (PDF)
                    </button>
                    <button type="button" class="btn btn-outline-primary">
                        <i class="bi bi-file-earmark-excel me-1"></i> تحميل الفاتورة (Excel)
                    </button>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- Reorder Modal -->
<div class="modal fade" id="reorderModal" tabindex="-1" aria-labelledby="reorderModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="reorderModalLabel">إعادة الطلب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>سيتم إضافة جميع المنتجات من طلبك السابق إلى سلة التسوق الخاصة بك.</p>
                <div class="alert alert-info">
                    <i class="bi bi-info-circle me-1"></i> ملاحظة: قد تتغير أسعار بعض المنتجات أو تتوفر بكميات محدودة.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <a href="{{ url('/cart') }}" class="btn btn-primary">إضافة للسلة والمتابعة</a>
            </div>
        </div>
    </div>
</div>

<style>
.order-tracking {
    position: relative;
    padding-right: 30px;
}

.order-tracking:before {
    content: '';
    position: absolute;
    top: 0;
    right: 15px;
    height: 100%;
    width: 2px;
    background-color: #e9ecef;
}

.tracking-step {
    position: relative;
    margin-bottom: 30px;
}

.tracking-point {
    position: absolute;
    right: 0;
    top: 0;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    z-index: 1;
}

.tracking-content {
    margin-right: 50px;
}

.tracking-date {
    font-size: 0.85rem;
    color: #6c757d;
    margin-bottom: 5px;
}

.tracking-title {
    font-weight: 600;
    margin-bottom: 5px;
}

.tracking-text {
    font-size: 0.9rem;
    color: #6c757d;
}

.tracking-step.active .tracking-point {
    box-shadow: 0 0 0 4px rgba(255, 193, 7, 0.2);
}
</style>
