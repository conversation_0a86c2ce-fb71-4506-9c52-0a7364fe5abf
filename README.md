
# متجر Ex الإلكتروني

متجر إلكتروني كامل وجذاب لبيع المنتجات باسم "Ex"، مصمم باللونين الأبيض والأسود مع دعم RTL كامل وخط كايرو.

## المميزات

- واجهة مستخدم جميلة وسهلة الاستخدام
- لوحة تحكم لمدير المتجر
- لوحة تحكم للعميل
- نظام تتبع الطلبات
- نظام سلة تسوق متكامل
- دعم RTL كامل مع خط كايرو
- تصميم عصري باللونين الأبيض والأسود

## التقنيات المستخدمة

- PHP 8.0+
- Laravel 9.x
- MySQL
- Bootstrap 5
- jQuery
- Font Awesome

## المتطلبات

- PHP 8.0 أو أعلى
- MySQL 5.7 أو أعلى
- Apache أو Nginx
- Composer

## التثبيت

1. استنساخ المستودع
2. تثبيت الاعتماديات عبر `composer install`
3. إنشاء ملف `.env` وتعديل إعدادات قاعدة البيانات
4. تشغيل `php artisan key:generate`
5. تشغيل `php artisan migrate`
6. تشغيل `php artisan serve`
7. فتح المتصفر على العنوان `http://localhost:8000`

## المساهمون

- فريق Ex للتطوير
